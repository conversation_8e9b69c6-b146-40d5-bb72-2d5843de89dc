import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { TextFormat } from '../../../helpers/fmt';
import { Grouping } from '../../../models/Grouping';
import { DeckBuilderGrouping } from './DeckBuilderGrouping';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockGrouping = vi.mocked(DeckActions.grouping);

type DeckBuilderGroupingProps = React.ComponentProps<typeof DeckBuilderGrouping>;

const defaultProps: Omit<DeckBuilderGroupingProps, 'dispatcher'> = {
  deckGrouping: Grouping.NONE,
};

const renderDeckBuilderGrouping = (props: Partial<DeckBuilderGroupingProps> = {}) => {
  return renderWithDispatcher(DeckBuilderGrouping, { ...defaultProps, ...props });
};

describe('DeckBuilderGrouping', () => {
  it('displays the group by heading', () => {
    renderDeckBuilderGrouping();
    expect(screen.getByText('Group By')).toBeInTheDocument();
  });

  it('displays the select dropdown', () => {
    renderDeckBuilderGrouping();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'Group by' })).toBeDisabled();
  });

  it('displays all grouping options', () => {
    renderDeckBuilderGrouping();

    // Check that all grouping options are present
    Object.keys(Grouping).forEach((key) => {
      const groupingValue = Grouping[key as keyof typeof Grouping];
      const expectedText = TextFormat.capitalize(groupingValue);
      const option = screen.getByRole('option', { name: expectedText });
      expect(option).toBeInTheDocument();
    });
  });

  it('calls DeckActions.grouping when selection changes', () => {
    const { dispatcher } = renderDeckBuilderGrouping();
    const select = screen.getByRole('combobox');

    fireEvent.change(select, { target: { value: Grouping.PRINTING } });

    expect(mockGrouping).toHaveBeenCalledWith(Grouping.PRINTING, dispatcher);
  });

  it('can preselect an option', () => {
    renderDeckBuilderGrouping({ deckGrouping: Grouping.NONE });
    const select = screen.getByRole('combobox');
    expect(select).toHaveValue(Grouping.NONE);
  });
});
