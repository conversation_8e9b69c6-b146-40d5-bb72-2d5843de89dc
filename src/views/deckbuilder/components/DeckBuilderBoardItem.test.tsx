import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckBoard, FakeDeckCard } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { Card } from '../../../models/Cards';
import { DeckCard } from '../../../models/Decks';
import { DeckBuilderBoardItem } from './DeckBuilderBoardItem';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockAddDeckCard = vi.mocked(DeckActions.addDeckCard);
const mockRemoveDeckCard = vi.mocked(DeckActions.removeDeckCard);
const mockRemoveDeckCards = vi.mocked(DeckActions.removeDeckCards);
const mockUpdateDeck = vi.mocked(DeckActions.updateDeck);

// Mock track helper
vi.mock('../../../helpers/ga_helper', () => ({
  default: vi.fn(),
}));

// Mock CoreAssets
vi.mock('../../../helpers/core_assets', () => ({
  CoreAssets: {
    imageHost: vi.fn(() => 'https://example.com'),
    cardBack: vi.fn(() => '/images/card-back.jpg'),
  },
}));

type DeckBuilderBoardItemProps = React.ComponentProps<typeof DeckBuilderBoardItem>;

const createMockCard = (name = 'Lightning Bolt'): Card => {
  return new Card({
    id: 1,
    name,
    jsonID: 'test-json-id',
  });
};

const createMockDeckCard = (): DeckCard => {
  return create(FakeDeckCard, {
    id: 1,
    cardId: 1,
    boardId: 1,
  });
};

const fakeDeck = create(FakeDeck, {
  cards: Immutable.Map([[1, createMockCard()]]),
  deckCards: Immutable.Map({ 1: createMockDeckCard() }),
});

const defaultProps: Omit<DeckBuilderBoardItemProps, 'dispatcher'> = {
  deck: fakeDeck,
  deckBoard: create(FakeDeckBoard, { id: 1, name: 'Main' }),
  deckCards: fakeDeck.get('deckCards').toList(),
  card: createMockCard(),
  cardCount: 1,
  showCount: true,
  mutable: true,
};

const renderDeckBuilderBoardItem = (props: Partial<DeckBuilderBoardItemProps> = {}) => {
  return renderWithDispatcher(DeckBuilderBoardItem, { ...defaultProps, ...props });
};

describe('DeckBuilderBoardItem', () => {
  it('displays the board item container', () => {
    const { container } = renderDeckBuilderBoardItem();
    expect(container.querySelector('.deckbuilder-board-item')).toBeInTheDocument();
  });

  it('displays the card image', () => {
    const { container } = renderDeckBuilderBoardItem();
    const cardJsonId = defaultProps.card.get('jsonID');
    const cardImage = container.querySelector(`img[src="https://example.com/card_images/${cardJsonId}.jpg"]`);
    expect(cardImage).toBeInTheDocument();
    expect(cardImage).toHaveAttribute('alt', 'Lightning Bolt');
  });

  it('displays placeholder image initially', () => {
    const { container } = renderDeckBuilderBoardItem();
    const placeholderImage = container.querySelector('img[src="/images/card-back.jpg"]');
    expect(placeholderImage).toBeInTheDocument();
  });

  it('shows card count when showCount is true', () => {
    renderDeckBuilderBoardItem({ showCount: true, cardCount: 0 });
    expect(screen.getByText('0')).toBeInTheDocument();
  });

  it('does not display card count when showCount is false', () => {
    renderDeckBuilderBoardItem({ showCount: false, cardCount: 3 });
    expect(screen.queryByText('3')).not.toBeInTheDocument();
  });

  describe('when board item is mutable', () => {
    test('I can increase the number of card', () => {
      renderDeckBuilderBoardItem({ mutable: true });
      const addOneButton = screen.getByText('Add one');
      expect(addOneButton).toBeInTheDocument();
      fireEvent.click(addOneButton);
      expect(mockAddDeckCard).toHaveBeenCalled();
    });

    test('I can decrease the number of card when card count is greater than 0', () => {
      renderDeckBuilderBoardItem({ mutable: true, cardCount: 2 });
      const removeOneButton = screen.getByText('Remove one');
      expect(removeOneButton).toBeInTheDocument();
      fireEvent.click(removeOneButton);
      expect(mockRemoveDeckCard).toHaveBeenCalled();
    });

    test('I CANNOT decrease the number of card when cardCount props is not set', () => {
      renderDeckBuilderBoardItem({ mutable: true, cardCount: undefined });
      const removeOneButton = screen.getByText('Remove one');
      expect(removeOneButton).toBeInTheDocument();
      fireEvent.click(removeOneButton);
      expect(mockRemoveDeckCards).not.toHaveBeenCalled();
    });

    test('I can remove all cards one by one', () => {
      renderDeckBuilderBoardItem({ mutable: true });
      const removeOneButton = screen.getByText('Remove one');
      expect(removeOneButton).toBeInTheDocument();
      fireEvent.click(removeOneButton);
      expect(screen.getByText('Are you sure?')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Remove')).toBeInTheDocument();
      fireEvent.click(screen.getByDisplayValue('Remove'));
      expect(mockRemoveDeckCards).toHaveBeenCalled();
    });

    test('I can remove all cards', () => {
      renderDeckBuilderBoardItem({ mutable: true });
      const removeAllButton = screen.getByText('Remove all');
      expect(removeAllButton).toBeInTheDocument();
      fireEvent.click(removeAllButton);
      expect(screen.getByText('Are you sure?')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Remove')).toBeInTheDocument();
      fireEvent.click(screen.getByDisplayValue('Remove'));
      expect(mockRemoveDeckCards).toHaveBeenCalled();
    });
  });

  describe('in playtest mode', () => {
    test('I can go in fullscreen mode', () => {
      renderDeckBuilderBoardItem({ playtest: true });
      fireEvent.click(screen.getByText('View'));
      expect(document.querySelector('.deckbuilder-board-item__overlay')).toBeInTheDocument();
    });

    test('I can go back to the top of the screen', () => {
      const onGotoTop = vi.fn();
      renderDeckBuilderBoardItem({ playtest: true, onGotoTop });
      fireEvent.click(screen.getByText('Return to Top'));
      expect(onGotoTop).toHaveBeenCalled();
    });

    test('I can go to the bottom of the screen', () => {
      const onGotoBottom = vi.fn();
      renderDeckBuilderBoardItem({ playtest: true, onGotoBottom });
      fireEvent.click(screen.getByText('Return to Bottom'));
      expect(onGotoBottom).toHaveBeenCalled();
    });

    test('I can go to graveyard ', () => {
      const onGotoGraveyard = vi.fn();
      renderDeckBuilderBoardItem({ playtest: true, onGotoGraveyard });
      fireEvent.click(screen.getByText('Graveyard'));
      expect(onGotoGraveyard).toHaveBeenCalled();
    });
  });

  describe('when board item is linkable', () => {
    it('displays the links button with links count', () => {
      renderDeckBuilderBoardItem({
        linkable: true,
        cardCount: 4,
      });

      // Check for the link count text using a more flexible approach
      expect(screen.getByText('1/4', { exact: false })).toBeInTheDocument();
      expect(screen.getByText('Links', { exact: false })).toBeInTheDocument();
    });

    it('calls onClickLink when link button is clicked', () => {
      const onClickLink = vi.fn();
      renderDeckBuilderBoardItem({ linkable: true, onClickLink });

      fireEvent.click(screen.getByText('Links', { exact: false }));
      expect(onClickLink).toHaveBeenCalled();
    });

    it('calls updateDeck when set cover art button is clicked', () => {
      renderDeckBuilderBoardItem({ linkable: true });

      fireEvent.click(
        screen.getByText('Set as Cover Art').closest('.deckbuilder-board-item__image-wrapper__hover__button')!,
      );

      expect(mockUpdateDeck).toHaveBeenCalled();
    });
  });

  describe('when board item is expandable', () => {
    test('I can view the card in fullscreen', () => {
      renderDeckBuilderBoardItem({ expandable: true });
      fireEvent.click(screen.getByText('View'));
      expect(document.querySelector('.deckbuilder-board-item__overlay')).toBeInTheDocument();
    });
  });

  it('opens the card in fullscreen when clicked', () => {
    const { container } = renderDeckBuilderBoardItem();
    fireEvent.click(container.querySelector('.deckbuilder-board-item')!);
    expect(document.querySelector('.deckbuilder-board-item__overlay')).toBeInTheDocument();
  });

  it('exits fullscreen view when overlay is clicked', () => {
    const { container } = renderDeckBuilderBoardItem();
    const cardItem = container.querySelector('.deckbuilder-board-item');

    if (cardItem) {
      fireEvent.click(cardItem);

      const overlay = container.querySelector('.deckbuilder-board-item__overlay');
      if (overlay) {
        fireEvent.click(overlay);

        expect(container.querySelector('.deckbuilder-board-item__overlay')).not.toBeInTheDocument();
      }
    }
  });

  it('calls custom onClick when provided and not in fullscreen', () => {
    const onClick = vi.fn();
    const { container } = renderDeckBuilderBoardItem({ onClick });

    const cardItem = container.querySelector('.deckbuilder-board-item');
    if (cardItem) {
      fireEvent.click(cardItem);
      expect(onClick).toHaveBeenCalled();
    }
  });

  it('updates card count when props change', () => {
    const { rerenderWithDispatcher } = renderDeckBuilderBoardItem({ cardCount: 2 });

    rerenderWithDispatcher({ cardCount: 5 });

    expect(screen.getByText('5')).toBeInTheDocument();
  });
});
